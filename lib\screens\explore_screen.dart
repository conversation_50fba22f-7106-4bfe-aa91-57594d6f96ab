import 'package:flutter/material.dart';
import 'package:wicker/widgets/post_card.dart';
import '../screens/detail_scroll_viewer.dart'; // To navigate to the detail view
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wicker/widgets/explore_post_card.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  _ExploreScreenState createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _searchResults = [];
  bool _isSearching = false;

  // We'll reuse the mock data structure from the home screen for now
  // In a real app, this data would come from its own API call
  final Map<String, List<Map<String, String>>> allData = {
    'Trending': [
      {
        'type': 'image',
        'title': 'Osu Night Market',
        'subtitle': 'Street Food',
        'imageUrl': 'https://picsum.photos/seed/osu/400/300',
        'mediaUrl': 'https://picsum.photos/seed/osu/400/300',
        'avatarUrl': 'https://i.pravatar.cc/150?u=osu',
        'posterName': 'AccraFoods',
        'views': '2.1k',
        'postedTime': '1 day ago',
        'category': 'Trending',
      },
      {
        'type': 'video',
        'title': 'Sandbox Beach Club',
        'subtitle': 'Beach Bar',
        'imageUrl': 'https://picsum.photos/seed/sandbox/400/300',
        'mediaUrl':
            'https://storage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
        'avatarUrl': 'https://i.pravatar.cc/150?u=sandbox',
        'posterName': 'GhanaVibes',
        'views': '10k',
        'postedTime': '3 days ago',
        'category': 'Trending',
      },
    ],
    'Playlists': [
      {
        'type': 'voice_note',
        'title': 'Accra\'s Best Kelewele',
        'subtitle': 'By @gaudking',
        'imageUrl': 'https://picsum.photos/seed/kelewele/400/300',
        'mediaUrl':
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
        'avatarUrl': 'https://i.pravatar.cc/150?u=gaudking',
        'posterName': 'Gaudking',
        'views': '5.5k',
        'postedTime': '5 days ago',
        'category': 'Playlists',
      },
      {
        'type': 'image',
        'title': 'Sunday Brunch Spots',
        'subtitle': 'By @chapper',
        'imageUrl': 'https://picsum.photos/seed/brunch/400/300',
        'mediaUrl': 'https://picsum.photos/seed/brunch/400/300',
        'avatarUrl': 'https://i.pravatar.cc/150?u=chapper',
        'posterName': 'Chapper',
        'views': '1.8k',
        'postedTime': '1 week ago',
        'category': 'Playlists',
      },
    ],
    'Community': [
      {
        'type': 'image',
        'title': 'Best tailor for Kente?',
        'subtitle': '15 comments',
        'imageUrl': 'https://picsum.photos/seed/kente/400/300',
        'mediaUrl': 'https://picsum.photos/seed/kente/400/300',
        'avatarUrl': 'https://i.pravatar.cc/150?u=kente',
        'posterName': 'Adwoa',
        'views': '987',
        'postedTime': '2 weeks ago',
        'category': 'Community',
      },
    ],
  };

  void _performSearch(String query) {
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
        _searchResults = [];
      });
      return;
    }
    setState(() {
      _isSearching = true;
      final allPosts = allData.values.expand((posts) => posts).toList();
      _searchResults = allPosts.where((post) {
        final titleLower = post['title']!.toLowerCase();
        final subtitleLower = post['subtitle']!.toLowerCase();
        final queryLower = query.toLowerCase();
        return titleLower.contains(queryLower) ||
            subtitleLower.contains(queryLower);
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text('Explore', style: TextStyle(color: Colors.black)),
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: TextField(
              controller: _searchController,
              onChanged: _performSearch,
              decoration: InputDecoration(
                hintText: 'Search places, services, items...',
                prefixIcon: const Icon(EvaIcons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(EvaIcons.closeCircle),
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.grey[200],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30.0),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          // Content Area
          Expanded(
            child: _isSearching
                ? _buildSearchResults()
                : _buildDefaultExploreContent(),
          ),
        ],
      ),
    );
  }

  // The content to show when the user is searching
  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return const Center(child: Text('No results found.'));
    }
    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final post = _searchResults[index];
        return GestureDetector(
          onTap: () {
            // Logic to navigate to the DetailScrollViewer
            final category = post['category']!;
            final itemIndex = allData[category]!.indexOf(post);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DetailScrollViewer(
                  allCategoriesData: allData,
                  initialCategoryIndex: allData.keys.toList().indexOf(category),
                  initialItemIndex: itemIndex,
                ),
              ),
            );
          },
          child: PostCard(postData: post),
        );
      },
    );
  }

  // The content to show when the search bar is empty
  Widget _buildDefaultExploreContent() {
    final allPosts = allData.values.expand((posts) => posts).toList()
      ..shuffle();

    return MasonryGridView.count(
      crossAxisCount: 2, // Two columns
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      padding: const EdgeInsets.all(12.0),
      itemCount: allPosts.length,
      itemBuilder: (context, index) {
        final post = allPosts[index];

        // THE FIX IS HERE: We wrap the card in an AspectRatio widget
        return AspectRatio(
          // We'll create a dynamic aspect ratio for the staggered effect
          // Even items will be shorter, odd items will be taller
          aspectRatio: index % 2 == 0 ? 1 / 1.2 : 1 / 1.5,
          child: GestureDetector(
            onTap: () {
              // Navigation logic remains the same
              final category = post['category']!;
              final itemIndex = allData[category]!.indexOf(post);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DetailScrollViewer(
                    allCategoriesData: allData,
                    initialCategoryIndex: allData.keys.toList().indexOf(
                      category,
                    ),
                    initialItemIndex: itemIndex,
                  ),
                ),
              );
            },
            child: ExplorePostCard(postData: post),
          ),
        );
      },
    );
  }
}
