import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'auth_service.dart';

class PlacesService {
  // Use ******** for Android Emulator, and localhost for iOS simulator/web
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://********:5000"
      : "http://127.0.0.1:5000";

  final AuthService _authService = AuthService();

  /// Submits a new place to the backend API
  /// Returns success message or throws an error
  Future<String> addPlace({
    required String name,
    required String category,
    required double latitude,
    required double longitude,
    double? rating,
    String? review,
  }) async {
    try {
      // Get the authentication token
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication required. Please log in.');
      }

      print('Attempting to add place to: $_baseUrl/api/places');
      
      final response = await http.post(
        Uri.parse('$_baseUrl/api/places'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'name': name,
          'category': category,
          'latitude': latitude,
          'longitude': longitude,
          'rating': rating,
          'review': review,
        }),
      );

      print('Add place response status: ${response.statusCode}');
      print('Add place response body: ${response.body}');

      if (response.statusCode == 201) {
        final responseBody = jsonDecode(response.body);
        return responseBody['msg'] ?? 'Place added successfully';
      } else {
        final responseBody = jsonDecode(response.body);
        throw Exception(responseBody['msg'] ?? 'Failed to add place');
      }
    } catch (e) {
      print('Add place error: $e');
      // If it's already an Exception we threw, re-throw it
      if (e is Exception) {
        rethrow;
      }
      // Otherwise, wrap network/parsing errors
      throw Exception('Failed to connect to server: $e');
    }
  }

  /// Gets all places from the backend API
  Future<List<Map<String, dynamic>>> getPlaces() async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication required. Please log in.');
      }

      print('Attempting to get places from: $_baseUrl/api/places');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/api/places'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        },
      );

      print('Get places response status: ${response.statusCode}');
      print('Get places response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(responseBody['places'] ?? []);
      } else {
        final responseBody = jsonDecode(response.body);
        throw Exception(responseBody['msg'] ?? 'Failed to get places');
      }
    } catch (e) {
      print('Get places error: $e');
      // If it's already an Exception we threw, re-throw it
      if (e is Exception) {
        rethrow;
      }
      // Otherwise, wrap network/parsing errors
      throw Exception('Failed to connect to server: $e');
    }
  }
}
