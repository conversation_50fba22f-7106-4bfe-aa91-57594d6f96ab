import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'auth_service.dart';

class PlacesService {
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://********:5000"
      : "http://127.0.0.1:5000";

  final AuthService _authService = AuthService();

  /// Submits a new place with images using a multipart request.
  /// Includes logic to automatically refresh the token and retry on failure.
  Future<String> addPlace({
    required String name,
    required String category,
    required double latitude,
    required double longitude,
    double? rating,
    String? review,
    List<XFile>? images,
  }) async {
    // This is a helper function to construct our multipart request
    Future<http.MultipartRequest> _createRequest() async {
      final token = await _authService.getAccessToken();
      if (token == null) throw Exception('Authentication Token not found.');

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/api/places/'),
      );
      request.headers['Authorization'] = 'Bearer $token';

      // Add text fields
      request.fields['name'] = name;
      request.fields['category'] = category;
      request.fields['latitude'] = latitude.toString();
      request.fields['longitude'] = longitude.toString();
      if (rating != null) request.fields['rating'] = rating.toString();
      if (review != null) request.fields['review'] = review;

      // Add images
      if (images != null && images.isNotEmpty) {
        for (var imageFile in images) {
          request.files.add(
            await http.MultipartFile.fromPath(
              'images',
              imageFile.path,
              contentType: MediaType('image', 'jpeg'),
            ),
          );
        }
      }
      return request;
    }

    try {
      // --- First Attempt ---
      var request = await _createRequest();
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      // --- Check for Expired Token ---
      if (response.statusCode == 401) {
        // Token expired, try to refresh
        bool refreshed = await _authService.refreshToken();
        if (refreshed) {
          // --- Retry Attempt ---
          print('Token refreshed, retrying request...');
          var retryRequest =
              await _createRequest(); // Create a fresh request with the new token
          var retryStreamedResponse = await retryRequest.send();
          response = await http.Response.fromStream(retryStreamedResponse);
        } else {
          throw Exception('Session expired. Please log in again.');
        }
      }

      // --- Handle Final Response ---
      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'] ?? 'Place added successfully';
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to add place');
      }
    } catch (e) {
      print('Add place error: $e');
      if (e is Exception) rethrow;
      throw Exception('An unknown error occurred: $e');
    }
  }
}
