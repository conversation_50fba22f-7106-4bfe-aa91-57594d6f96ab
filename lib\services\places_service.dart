import 'dart:convert';
import 'package:flutter/foundation.dart'; // Import this to check for web platform (kIsWeb)
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'auth_service.dart';

class PlacesService {
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://********:5000"
      : "http://127.0.0.1:5000";

  final AuthService _authService = AuthService();

  Future<String> addPlace({
    required String name,
    required String category,
    required double latitude,
    required double longitude,
    double? rating,
    String? review,
    List<XFile>? images,
  }) async {
    // This helper function now contains the platform-specific logic
    Future<http.MultipartRequest> _createRequest() async {
      final token = await _authService.getAccessToken();
      if (token == null) throw Exception('Authentication Token not found.');

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/api/places/'),
      );
      request.headers['Authorization'] = 'Bearer $token';

      // Text fields
      request.fields['name'] = name;
      request.fields['category'] = category;
      request.fields['latitude'] = latitude.toString();
      request.fields['longitude'] = longitude.toString();
      if (rating != null) request.fields['rating'] = rating.toString();
      if (review != null) request.fields['review'] = review;

      // THE FIX: Handle images differently based on the platform
      if (images != null && images.isNotEmpty) {
        for (var imageFile in images) {
          if (kIsWeb) {
            // For web, read bytes and use fromBytes
            var bytes = await imageFile.readAsBytes();
            request.files.add(
              http.MultipartFile.fromBytes(
                'images',
                bytes,
                filename: imageFile.name, // Important to provide a filename
                contentType: MediaType('image', 'jpeg'),
              ),
            );
          } else {
            // For mobile, use fromPath
            request.files.add(
              await http.MultipartFile.fromPath(
                'images',
                imageFile.path,
                contentType: MediaType('image', 'jpeg'),
              ),
            );
          }
        }
      }
      return request;
    }

    // The rest of the try/catch/retry logic remains the same
    try {
      var request = await _createRequest();
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 401) {
        bool refreshed = await _authService.refreshToken();
        if (refreshed) {
          print('Token refreshed, retrying request...');
          var retryRequest = await _createRequest();
          var retryStreamedResponse = await retryRequest.send();
          response = await http.Response.fromStream(retryStreamedResponse);
        } else {
          throw Exception('Session expired. Please log in again.');
        }
      }

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'] ?? 'Place added successfully';
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to add place');
      }
    } catch (e) {
      print('Add place error: $e');
      if (e is Exception) rethrow;
      throw Exception('An unknown error occurred: $e');
    }
  }
}
