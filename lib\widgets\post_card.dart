import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class PostCard extends StatelessWidget {
  final Map<String, String> postData;

  const PostCard({super.key, required this.postData});

  @override
  Widget build(BuildContext context) {
    // Using ?? to provide default values and prevent null errors
    final imageUrl =
        postData['imageUrl'] ??
        'https://via.placeholder.com/400x210.png?text=No+Image';
    final avatarUrl =
        postData['avatarUrl'] ?? 'https://via.placeholder.com/150.png?text=N/A';
    final title = postData['title'] ?? 'Untitled Post';
    final posterName = postData['posterName'] ?? 'Unknown';
    final views = postData['views'] ?? '0';
    final postedTime = postData['postedTime'] ?? 'long ago';

    return Card(
      elevation: 0,
      margin: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.network(
            imageUrl,
            height: 210,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 12, 12, 24),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: NetworkImage(avatarUrl),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "$posterName • $views views • $postedTime",
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(EvaIcons.moreVertical, color: Colors.grey),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
