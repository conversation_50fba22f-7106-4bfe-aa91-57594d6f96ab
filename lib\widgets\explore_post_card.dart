import 'package:flutter/material.dart';

class ExplorePostCard extends StatelessWidget {
  final Map<String, String> postData;

  const ExplorePostCard({super.key, required this.postData});

  @override
  Widget build(BuildContext context) {
    final imageUrl =
        postData['imageUrl'] ??
        'https://via.placeholder.com/400.png?text=No+Image';
    final title = postData['title'] ?? 'Untitled Post';

    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Stack(
        fit: StackFit.expand, // Make the Stack fill the Card
        children: [
          Image.network(
            imageUrl,
            fit: BoxFit.cover, // Let BoxFit handle the filling
          ),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
