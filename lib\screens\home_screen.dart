import 'package:flutter/material.dart';
import 'package:wicker/services/places_service.dart';
import '../widgets/home_search_bar.dart';
import '../widgets/post_card.dart';
import '../widgets/filter_chip.dart';
import 'detail_scroll_viewer.dart';

class HomeScreen extends StatefulWidget {
  final VoidCallback onSearchTap;
  const HomeScreen({super.key, required this.onSearchTap});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PlacesService _placesService = PlacesService();
  late Future<List<Map<String, dynamic>>> _placesFuture;

  // --- MOCK DATA with updated avatar URLs ---
  // final Map<String, List<Map<String, String>>> allData = {
  //   'Trending': [
  //     {
  //       'type': 'image',
  //       'title': 'Osu Night Market',
  //       'subtitle': 'Street Food',
  //       'imageUrl': 'https://picsum.photos/seed/osu/400/300',
  //       'mediaUrl': 'https://picsum.photos/seed/osu/800/1200',
  //       'avatarUrl': 'https://picsum.photos/seed/avatar_osu/100',
  //       'posterName': 'AccraFoods',
  //       'views': '2.1k',
  //       'postedTime': '1 day ago',
  //       'category': 'Trending',
  //     },
  //     {
  //       'type': 'video',
  //       'title': 'Sandbox Beach Club',
  //       'subtitle': 'Beach Bar',
  //       'imageUrl': 'https://picsum.photos/seed/sandbox/400/300',
  //       'mediaUrl':
  //           'https://storage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
  //       'avatarUrl': 'https://picsum.photos/seed/avatar_sandbox/100',
  //       'posterName': 'GhanaVibes',
  //       'views': '10k',
  //       'postedTime': '3 days ago',
  //       'category': 'Trending',
  //     },
  //   ],
  //   'Playlists': [
  //     {
  //       'type': 'voice_note',
  //       'title': 'Accra\'s Best Kelewele',
  //       'subtitle': 'By @gaudking',
  //       'imageUrl': 'https://picsum.photos/seed/kelewele/400/300',
  //       'mediaUrl':
  //           'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
  //       'avatarUrl': 'https://picsum.photos/seed/avatar_gaudking/100',
  //       'posterName': 'Gaudking',
  //       'views': '5.5k',
  //       'postedTime': '5 days ago',
  //       'category': 'Playlists',
  //     },
  //     {
  //       'type': 'image',
  //       'title': 'Sunday Brunch Spots',
  //       'subtitle': 'By @chapper',
  //       'imageUrl': 'https://picsum.photos/seed/brunch/400/300',
  //       'mediaUrl': 'https://picsum.photos/seed/brunch/800/1200',
  //       'avatarUrl': 'https://picsum.photos/seed/avatar_chapper/100',
  //       'posterName': 'Chapper',
  //       'views': '1.8k',
  //       'postedTime': '1 week ago',
  //       'category': 'Playlists',
  //     },
  //   ],
  //   'Community': [
  //     {
  //       'type': 'image',
  //       'title': 'Best tailor for Kente?',
  //       'subtitle': '15 comments',
  //       'imageUrl': 'https://picsum.photos/seed/kente/400/300',
  //       'mediaUrl': 'https://picsum.photos/seed/kente/800/1200',
  //       'avatarUrl': 'https://picsum.photos/seed/avatar_kente/100',
  //       'posterName': 'Adwoa',
  //       'views': '987',
  //       'postedTime': '2 weeks ago',
  //       'category': 'Community',
  //     },
  //   ],
  // };

  @override
  void initState() {
    super.initState();
    // Fetch places when the screen is first loaded
    _placesFuture = _placesService.getPlaces();
  }

  // Helper to group places by category for the themed rows
  Map<String, List<Map<String, dynamic>>> _groupPlacesByCategory(
    List<Map<String, dynamic>> places,
  ) {
    Map<String, List<Map<String, dynamic>>> grouped = {};
    for (var place in places) {
      String category = place['category'] ?? 'Other';
      // Here you might want to add default values for missing keys from the DB
      place['imageUrl'] = (place['photos'] as List).isNotEmpty
          ? 'http://127.0.0.1:5000/${place['photos'][0]}'
          : 'https://via.placeholder.com/400x300.png?text=No+Image';
      place['avatarUrl'] =
          'https://picsum.photos/seed/${place['created_by']['\$oid']}/100';
      place['posterName'] = 'A User'; // Replace with actual user data later
      place['views'] = '0';
      place['postedTime'] = 'Just now';

      if (grouped.containsKey(category)) {
        grouped[category]!.add(place);
      } else {
        grouped[category] = [place];
      }
    }
    return grouped;
  }
  // void _filterPosts(String filter) {
  //   setState(() {
  //     _selectedFilter = filter;
  //     if (filter == 'All') {
  //       _displayedPosts = _getAllPosts();
  //     } else {
  //       _displayedPosts = allData[filter] ?? [];
  //     }
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _placesFuture,
        builder: (context, snapshot) {
          // While loading, show a spinner
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          // If there's an error
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          // If data is empty or null
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No places found. Add one!'));
          }

          // If data is loaded successfully
          final allData = _groupPlacesByCategory(snapshot.data!);
          final filters = allData.keys.toList();

          return Column(
            children: [
              Container(
                padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
                child: Column(
                  children: [
                    HomeSearchBar(onTap: widget.onSearchTap),
                    // For now, the filter chips are static. We can connect them later.
                    // ...
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: filters.length,
                  itemBuilder: (context, index) {
                    final category = filters[index];
                    final posts = allData[category]!;
                    // We can reuse the PostCard or ThemedRow here
                    // For simplicity, let's just show the post cards directly
                    return PostCard(
                      postData: posts[0].map(
                        (k, v) => MapEntry(k, v.toString()),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
