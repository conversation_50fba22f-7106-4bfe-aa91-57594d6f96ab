import 'package:flutter/material.dart';
import '../widgets/home_search_bar.dart';
import '../widgets/post_card.dart';
import '../widgets/filter_chip.dart';
import 'detail_scroll_viewer.dart';

class HomeScreen extends StatefulWidget {
  final VoidCallback onSearchTap;
  const HomeScreen({super.key, required this.onSearchTap});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // State for the selected filter and the list of posts to show
  String _selectedFilter = 'All';
  late List<Map<String, String>> _displayedPosts;

  // --- MOCK DATA ---
  // Note: Data now includes avatarUrl, posterName, views, postedTime, and category
  final Map<String, List<Map<String, String>>> allData = {
    'Trending': [
      {
        'type': 'image',
        'title': 'Osu Night Market',
        'subtitle': 'Street Food',
        'imageUrl': 'https://picsum.photos/seed/osu/400/300',
        'mediaUrl': 'https://picsum.photos/seed/osu/800/1200',
        'avatarUrl': 'https://i.pravatar.cc/150?u=osu',
        'posterName': 'AccraFoods',
        'views': '2.1k',
        'postedTime': '1 day ago',
        'category': 'Trending',
      },
      {
        'type': 'video',
        'title': 'Sandbox Beach Club',
        'subtitle': 'Beach Bar',
        'imageUrl': 'https://picsum.photos/seed/sandbox/400/300',
        'mediaUrl':
            'https://storage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
        'avatarUrl': 'https://i.pravatar.cc/150?u=sandbox',
        'posterName': 'GhanaVibes',
        'views': '10k',
        'postedTime': '3 days ago',
        'category': 'Trending',
      },
    ],
    'Playlists': [
      {
        'type': 'voice_note',
        'title': 'Accra\'s Best Kelewele',
        'subtitle': 'By @gaudking',
        'imageUrl': 'https://picsum.photos/seed/kelewele/400/300',
        'mediaUrl':
            'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
        'avatarUrl': 'https://i.pravatar.cc/150?u=gaudking',
        'posterName': 'Gaudking',
        'views': '5.5k',
        'postedTime': '5 days ago',
        'category': 'Playlists',
      },
      {
        'type': 'image',
        'title': 'Sunday Brunch Spots',
        'subtitle': 'By @chapper',
        'imageUrl': 'https://picsum.photos/seed/brunch/400/300',
        'mediaUrl': 'https://picsum.photos/seed/brunch/800/1200',
        'avatarUrl': 'https://i.pravatar.cc/150?u=chapper',
        'posterName': 'Chapper',
        'views': '1.8k',
        'postedTime': '1 week ago',
        'category': 'Playlists',
      },
    ],
    'Community': [
      {
        'type': 'image',
        'title': 'Best tailor for Kente?',
        'subtitle': '15 comments',
        'imageUrl': 'https://picsum.photos/seed/kente/400/300',
        'mediaUrl': 'https://picsum.photos/seed/kente/800/1200',
        'avatarUrl': 'https://i.pravatar.cc/150?u=kente',
        'posterName': 'Adwoa',
        'views': '987',
        'postedTime': '2 weeks ago',
        'category': 'Community',
      },
    ],
  };

  @override
  void initState() {
    super.initState();
    // Initially, show all posts
    _filterPosts('All');
  }

  // Flattens the map of data into a single list
  List<Map<String, String>> _getAllPosts() {
    return allData.values.expand((posts) => posts).toList();
  }

  // Updates the state based on the selected filter
  void _filterPosts(String filter) {
    setState(() {
      _selectedFilter = filter;
      if (filter == 'All') {
        _displayedPosts = _getAllPosts();
      } else {
        _displayedPosts = allData[filter] ?? [];
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final filters = ['All', ...allData.keys];

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Header section with Search and Filters
          Container(
            padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
            child: Column(
              children: [
                HomeSearchBar(onTap: widget.onSearchTap),
                // Filter Chip Bar
                SizedBox(
                  height: 50,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    itemCount: filters.length,
                    itemBuilder: (context, index) {
                      final filter = filters[index];
                      return CustomFilterChip(
                        label: filter,
                        isSelected: _selectedFilter == filter,
                        onTap: () => _filterPosts(filter),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Body section with the vertical post feed
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: _displayedPosts.length,
              itemBuilder: (context, index) {
                final post = _displayedPosts[index];
                return GestureDetector(
                  onTap: () {
                    // Find the original category and index to pass to DetailScrollViewer
                    final category = post['category']!;
                    final originalCategoryList = allData[category]!;
                    final itemIndex = originalCategoryList.indexOf(post);

                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DetailScrollViewer(
                          allCategoriesData: allData,
                          initialCategoryIndex: allData.keys.toList().indexOf(
                            category,
                          ),
                          initialItemIndex: itemIndex,
                        ),
                      ),
                    );
                  },
                  child: PostCard(postData: post),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
