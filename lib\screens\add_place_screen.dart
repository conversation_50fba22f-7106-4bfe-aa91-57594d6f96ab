import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import '../widgets/action_toolbar_button.dart';
import '../widgets/custom_map_marker.dart';
import '../services/places_service.dart';
import '../main.dart';

class AddPlaceScreen extends StatefulWidget {
  const AddPlaceScreen({super.key});

  @override
  _AddPlaceScreenState createState() => _AddPlaceScreenState();
}

class _AddPlaceScreenState extends State<AddPlaceScreen> {
  final MapController _mapController = MapController();
  final PlacesService _placesService = PlacesService();

  // State variables
  LatLng _currentMapCenter = const LatLng(5.6037, -0.1870);
  String? _placeName;
  String? _category;
  double? _rating;
  String? _review;
  bool _isLoading = false;

  // Data for our fixed list of categories
  final List<Map<String, dynamic>> _categories = [
    {'name': 'Restaurant', 'icon': Icons.restaurant_menu},
    {'name': 'Cafe', 'icon': Icons.local_cafe},
    {'name': 'Shop', 'icon': Icons.store},
    {'name': 'Art Gallery', 'icon': Icons.palette},
    {'name': 'Other', 'icon': EvaIcons.pin},
  ];

  /// Submits the place data to the backend
  Future<void> _submitPlace() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Step 1: Validate input
      if (_placeName == null || _placeName!.isEmpty) {
        throw Exception('Please enter a name for the place');
      }
      if (_category == null) {
        throw Exception('Please select a category for the place');
      }

      // Step 2: Submit to backend using PlacesService
      await _placesService.addPlace(
        name: _placeName!,
        category: _category!,
        latitude: _currentMapCenter.latitude,
        longitude: _currentMapCenter.longitude,
        rating: _rating,
        review: _review,
      );

      // Step 3: Show success message and navigate back
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Place added successfully!')),
        );
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // --- DIALOG AND BOTTOM SHEET FUNCTIONS ---

  // Shows a dialog to enter the place name
  void _showNameInputDialog() {
    final nameController = TextEditingController(text: _placeName);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Name of the Place'),
        content: TextField(
          controller: nameController,
          autofocus: true,
          decoration: const InputDecoration(hintText: 'Enter name'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _placeName = nameController.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Shows a dialog to add a star rating
  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add a Rating'),
        content: Center(
          child: RatingBar.builder(
            initialRating: _rating ?? 0,
            minRating: 1,
            allowHalfRating: true,
            itemBuilder: (context, _) =>
                const Icon(Icons.star, color: Colors.amber),
            onRatingUpdate: (newRating) {
              setState(() => _rating = newRating);
              Navigator.pop(context);
            },
          ),
        ),
      ),
    );
  }

  // Shows a bottom sheet for selecting a category
  void _showCategoryPickerDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return ListView.builder(
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return ListTile(
              leading: Icon(category['icon']),
              title: Text(category['name']),
              onTap: () {
                setState(() => _category = category['name']);
                Navigator.pop(context);
              },
            );
          },
        );
      },
    );
  }

  // Shows a dialog for writing a review
  void _showReviewInputDialog() {
    final reviewController = TextEditingController(text: _review);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add a Review'),
        content: TextField(
          controller: reviewController,
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Share your experience...',
          ),
          maxLines: 5, // Allow for multi-line input
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _review = reviewController.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // --- BUILD METHOD AND HELPERS ---

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: _currentMapCenter,
              initialZoom: 14.0,
              onPositionChanged: (position, hasGesture) {
                if (hasGesture) {
                  setState(() => _currentMapCenter = position.center);
                }
              },
            ),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.wicker.app',
              ),
            ],
          ),

          Center(
            child: CustomMapMarker(
              placeName: _placeName,
              category: _category, // Pass the category to the marker
              rating: _rating,
            ),
          ),
          _buildTopButtons(),
          _buildActionToolbar(),
        ],
      ),
    );
  }

  Widget _buildActionToolbar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(8.0),
        color: Colors.black.withValues(alpha: 0.5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            ActionToolbarButton(
              icon: EvaIcons.text,
              label: 'Name',
              onTap: _showNameInputDialog,
            ),
            ActionToolbarButton(
              icon: EvaIcons.grid,
              label: 'Category',
              onTap: _showCategoryPickerDialog,
            ),
            ActionToolbarButton(
              icon: EvaIcons.messageSquare,
              label: 'Review',
              onTap: _showReviewInputDialog,
            ),
            ActionToolbarButton(
              icon: EvaIcons.star,
              label: 'Rating',
              onTap: _showRatingDialog,
            ),
          ],
        ),
      ),
    );
  }

  // The rest of the helper methods (_buildTopButtons, _showNameInputDialog, _showRatingDialog)
  // remain the same as the previous step. For brevity, I've omitted them here,
  // but you should keep them in your file. I'll paste them below for completeness.

  Widget _buildTopButtons() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FloatingActionButton(
              heroTag: 'backBtn',
              mini: true,
              backgroundColor: Colors.white,
              onPressed: () => Navigator.of(context).pop(),
              child: const Icon(EvaIcons.arrowBack, color: Colors.black),
            ),
            FloatingActionButton.extended(
              heroTag: 'confirmBtn',
              backgroundColor: Colors.teal,
              onPressed: _isLoading ? null : _submitPlace,
              label: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Submit Place',
                      style: TextStyle(color: Colors.white),
                    ),
              icon: _isLoading
                  ? null
                  : const Icon(EvaIcons.checkmark, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
