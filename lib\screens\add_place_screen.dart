import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class AddPlaceScreen extends StatefulWidget {
  const AddPlaceScreen({super.key});

  @override
  _AddPlaceScreenState createState() => _AddPlaceScreenState();
}

class _AddPlaceScreenState extends State<AddPlaceScreen> {
  // Controller to interact with the map
  final MapController _mapController = MapController();
  
  // This will hold the coordinates of the center of the map
  LatLng _currentMapCenter = const LatLng(5.6037, -0.1870); // Default to <PERSON><PERSON><PERSON>

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // We use a Stack to layer the map, the marker, and the UI buttons
      body: Stack(
        children: [
          // Layer 1: The Map
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: _currentMapCenter,
              initialZoom: 14.0,
              // This callback is triggered whenever the map is moved
              onPositionChanged: (position, hasGesture) {
                if (hasGesture) {
                  setState(() {
                    _currentMapCenter = position.center!;
                  });
                }
              },
            ),
            children: [
              // The visual layer of the map from OpenStreetMap
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.wicker.app', // Replace with your app's package name
              ),
            ],
          ),

          // Layer 2: The Center Marker Pin
          // This marker stays in the center while the map moves underneath it
          const Center(
            child: Icon(
              EvaIcons.pin,
              size: 50,
              color: Colors.red,
            ),
          ),
          
          // Layer 3: The 'Back' and 'Confirm' buttons
          _buildTopButtons(),
        ],
      ),
    );
  }

  // A helper widget for the buttons at the top of the screen
  Widget _buildTopButtons() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back Button
            FloatingActionButton(
              heroTag: 'backBtn',
              mini: true,
              backgroundColor: Colors.white,
              onPressed: () => Navigator.of(context).pop(),
              child: const Icon(EvaIcons.arrowBack, color: Colors.black),
            ),
            // Confirm Location Button
            FloatingActionButton.extended(
              heroTag: 'confirmBtn',
              backgroundColor: Colors.teal,
              onPressed: () {
                // This is where we'll proceed to the next step
                print('Location confirmed: $_currentMapCenter');
                // For now, we'll just show a snackbar
                 ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Location selected: ${_currentMapCenter.latitude.toStringAsFixed(4)}, ${_currentMapCenter.longitude.toStringAsFixed(4)}')),
                );
              },
              label: const Text('Confirm Location', style: TextStyle(color: Colors.white)),
              icon: const Icon(EvaIcons.checkmark, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}