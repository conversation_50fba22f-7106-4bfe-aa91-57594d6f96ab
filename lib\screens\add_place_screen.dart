import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:wicker/main.dart';
import 'dart:convert';
import '../widgets/action_toolbar_button.dart';
import '../widgets/custom_map_marker.dart';

class AddPlaceScreen extends StatefulWidget {
  const AddPlaceScreen({super.key});

  @override
  _AddPlaceScreenState createState() => _AddPlaceScreenState();
}

class _AddPlaceScreenState extends State<AddPlaceScreen> {
  final MapController _mapController = MapController();

  // State variables
  LatLng _currentMapCenter = const LatLng(5.6037, -0.1870);
  String? _placeName;
  String? _category;
  double? _rating;
  String? _review;

  // Data for our fixed list of categories
  final List<Map<String, dynamic>> _categories = [
    {'name': 'Restaurant', 'icon': Icons.restaurant_menu},
    {'name': 'Cafe', 'icon': Icons.local_cafe},
    {'name': 'Shop', 'icon': Icons.store},
    {'name': 'Art Gallery', 'icon': Icons.palette},
    {'name': 'Other', 'icon': EvaIcons.pin},
  ];

 final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://10.0.2.2:5000"
      : "http://127.0.0.1:5000";

  Future<bool> submitData({required Map<String, dynamic> placeData}) async {
    try {
      print('Attempting submit to: $_baseUrl/api/places/add');
      final response = await http.post(
        Uri.parse('$_baseUrl/api/places/add'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'name': placeData['name'],
          'category': placeData['category'],
          'rating': placeData['rating'],
          'review': placeData['review'],
          'location': placeData['location'],
        }),
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        print(responseBody['msg']);
        return true;
      } else {
        final responseBody = jsonDecode(response.body);
        throw Exception(responseBody['msg']);
      }
    } catch (e) {
      print('Submit error: $e');
      if (e is Exception) {
        rethrow;
      }
  
      throw Exception('Failed to submit: $e');
    }
  }
}


  bool _isLoading = false;

  void _submitPlace() {
    
    setState(() {
      _isLoading = true;
    });
    try {
      // Step 1: Validate input
      if (_placeName == null || _placeName!.isEmpty) {
        throw Exception('Please enter a name for the place');
      }
      if (_category == null) {
        throw Exception('Please select a category for the place');
      }


      // Step 2: Prepare data to send to the backend
      final placeData = {
        'name': _placeName,
        'category':_category,
        'rating': _rating,
        'review': _review,
        'location': {
          'latitude': _currentMapCenter.latitude,
          'longitude': _currentMapCenter.longitude,
        },
      };

      // Step 3: Send data to the backend
      // TODO: Implement the API call to submit the place data
      // For now, we'll just print the data
      print('Submitting place data: $placeData');


    try {
      await _submitData(
        placeData: placeData,
      );

     
        // Navigate to the main app screen on successful login
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainScreen()),
      );
    
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }


  }
  








  // --- DIALOG AND BOTTOM SHEET FUNCTIONS ---

  // Shows a dialog to enter the place name
  void _showNameInputDialog() {
    final nameController = TextEditingController(text: _placeName);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Name of the Place'),
        content: TextField(
          controller: nameController,
          autofocus: true,
          decoration: const InputDecoration(hintText: 'Enter name'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _placeName = nameController.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Shows a dialog to add a star rating
  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add a Rating'),
        content: Center(
          child: RatingBar.builder(
            initialRating: _rating ?? 0,
            minRating: 1,
            allowHalfRating: true,
            itemBuilder: (context, _) =>
                const Icon(Icons.star, color: Colors.amber),
            onRatingUpdate: (newRating) {
              setState(() => _rating = newRating);
              Navigator.pop(context);
            },
          ),
        ),
      ),
    );
  }

  // Shows a bottom sheet for selecting a category
  void _showCategoryPickerDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return ListView.builder(
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return ListTile(
              leading: Icon(category['icon']),
              title: Text(category['name']),
              onTap: () {
                setState(() => _category = category['name']);
                Navigator.pop(context);
              },
            );
          },
        );
      },
    );
  }

  // Shows a dialog for writing a review
  void _showReviewInputDialog() {
    final reviewController = TextEditingController(text: _review);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add a Review'),
        content: TextField(
          controller: reviewController,
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Share your experience...',
          ),
          maxLines: 5, // Allow for multi-line input
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _review = reviewController.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // --- BUILD METHOD AND HELPERS ---

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: _currentMapCenter,
              initialZoom: 14.0,
              onPositionChanged: (position, hasGesture) {
                if (hasGesture)
                  setState(() => _currentMapCenter = position.center!);
              },
            ),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.wicker.app',
              ),
            ],
          ),
          
          Center(
            child: CustomMapMarker(
              placeName: _placeName,
              category: _category, // Pass the category to the marker
              rating: _rating,
            ),
          ),
          _buildTopButtons(),
          _buildActionToolbar(),
          _isLoading
                ? const CircularProgressIndicator() : const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildActionToolbar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(8.0),
        color: Colors.black.withOpacity(0.5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            ActionToolbarButton(
              icon: EvaIcons.text,
              label: 'Name',
              onTap: _showNameInputDialog,
            ),
            ActionToolbarButton(
              icon: EvaIcons.grid,
              label: 'Category',
              onTap: _showCategoryPickerDialog,
            ),
            ActionToolbarButton(
              icon: EvaIcons.messageSquare,
              label: 'Review',
              onTap: _showReviewInputDialog,
            ),
            ActionToolbarButton(
              icon: EvaIcons.star,
              label: 'Rating',
              onTap: _showRatingDialog,
            ),
          ],
        ),
      ),
    );
  }

  // The rest of the helper methods (_buildTopButtons, _showNameInputDialog, _showRatingDialog)
  // remain the same as the previous step. For brevity, I've omitted them here,
  // but you should keep them in your file. I'll paste them below for completeness.

  Widget _buildTopButtons() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FloatingActionButton(
              heroTag: 'backBtn',
              mini: true,
              backgroundColor: Colors.white,
              onPressed: () => Navigator.of(context).pop(),
              child: const Icon(EvaIcons.arrowBack, color: Colors.black),
            ),
            FloatingActionButton.extended(
              heroTag: 'confirmBtn',
              backgroundColor: Colors.teal,
              onPressed: () {
                print('Submitting Place:');
                print('Location: $_currentMapCenter');
                print('Name: $_placeName');
                print('Category: $_category');
                print('Rating: $_rating');
                print('Review: $_review');
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Place submitted!')),
                );
              },
              label: const Text(
                'Submit Place',
                style: TextStyle(color: Colors.white),
              ),
              icon: const Icon(EvaIcons.checkmark, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
