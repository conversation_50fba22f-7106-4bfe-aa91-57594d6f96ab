import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthService {
  // Use ******** for Android Emulator, and localhost for iOS simulator/web
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://********:5000"
      : "http://127.0.0.1:5000";

  final _storage = const FlutterSecureStorage();
    static const _accessTokenKey = 'access_token';
  static const _refreshTokenKey = 'refresh_token';

  /// Saves the auth token securely
  Future<void> _saveToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }


  // Helper to get the access token
  Future<String?> getAccessToken() async {
    return await _storage.read(key: _accessTokenKey);
  }


  /// Handles user signup by calling the backend API.
  /// Returns a success message or throws an error.
  Future<String> signup({
    required String username,
    required String email,
    required String password,
  }) async {
    try {
      print('Attempting signup to: $_baseUrl/api/auth/register');
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/register'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(<String, String>{
          'username': username,
          'email': email,
          'password': password,
        }),
      );

      print('Signup response status: ${response.statusCode}');
      print('Signup response body: ${response.body}');

      final responseBody = jsonDecode(response.body);

      if (response.statusCode == 201) {
        return responseBody['msg']; // "User created successfully"
      } else {
        // Throws an error with the message from the backend (e.g., "Email already exists")
        throw Exception(responseBody['msg']);
      }
    } catch (e) {
      print('Signup error: $e');
      // If it's already an Exception we threw, re-throw it
      if (e is Exception) {
        rethrow;
      }
      // Otherwise, wrap network/parsing errors
      throw Exception('Failed to connect to server: $e');
    }
  }

  /// Handles user login by calling the backend API.
  /// Saves the token on success.
  /// Returns true on success or throws an error.
  Future<bool> login({required String email, required String password}) async {
    try {
      print('Attempting login to: $_baseUrl/api/auth/login');
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/login'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(<String, String>{
          'email': email,
          'password': password,
        }),
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        await _saveToken(responseBody['access_token']);
        return true;
      } else {
        final responseBody = jsonDecode(response.body);
        // Throws an error with the message from the backend (e.g., "Bad email or password")
        throw Exception(responseBody['msg']);
      }
    } catch (e) {
      print('Login error: $e');
      // If it's already an Exception we threw, re-throw it
      if (e is Exception) {
        rethrow;
      }
      // Otherwise, wrap network/parsing errors
      throw Exception('Failed to connect to server: $e');
    }
  }
  // THE CHANGE: Now deletes both tokens
  Future<void> logout() async {
    await _storage.delete(key: _accessTokenKey);
    await _storage.delete(key: _refreshTokenKey);
  }

  // NEW METHOD: Handles the token refresh logic
  Future<bool> refreshToken() async {
    final refreshToken = await _storage.read(key: _refreshTokenKey);
    if (refreshToken == null) {
      // If there's no refresh token, we can't refresh. Force logout.
      await logout();
      return false;
    }

    final response = await http.post(
      Uri.parse('$_baseUrl/api/auth/refresh'),
      headers: { 'Authorization': 'Bearer $refreshToken' },
    );

    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);
      // Save the new access token
      await _storage.write(key: _accessTokenKey, value: responseBody['access_token']);
      return true;
    } else {
      // If refresh fails, the refresh token is likely expired or invalid. Force logout.
      await logout();
      return false;
    }
  }

}

